#!/usr/bin/env python3
"""
Startup script for the FastAPI e-commerce backend.
This script handles dependency installation and application startup.
"""
import sys
import os
import subprocess
import importlib.util

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version.split()[0]} detected")
    return True

def install_package(package):
    """Install a package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_and_install_dependencies():
    """Check and install required dependencies."""
    required_packages = [
        "fastapi==0.104.1",
        "uvicorn[standard]==0.24.0",
        "sqlalchemy==2.0.23",
        "python-dotenv==1.0.0",
        "pydantic-settings==2.0.3",
        "python-jose[cryptography]==3.3.0",
        "passlib[bcrypt]==1.7.4",
        "python-multipart==0.0.6"
    ]
    
    print("🔍 Checking dependencies...")
    
    # Check if packages are available
    missing_packages = []
    for package in required_packages:
        package_name = package.split("==")[0].split("[")[0]
        if importlib.util.find_spec(package_name.replace("-", "_")) is None:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"📦 Installing {len(missing_packages)} missing packages...")
        for package in missing_packages:
            print(f"Installing {package}...")
            if not install_package(package):
                print(f"❌ Failed to install {package}")
                return False
        print("✅ All dependencies installed")
    else:
        print("✅ All dependencies are already installed")
    
    return True

def create_directories():
    """Create necessary directories."""
    directories = ["logs", "uploads"]
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"✅ Created directory: {directory}")

def test_application():
    """Test if the application can be imported."""
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # Test configuration
        from app.config.settings import get_settings
        settings = get_settings()
        print(f"✅ Configuration loaded: {settings.app_name}")
        
        # Test database
        from app.database.connection import engine
        print("✅ Database engine created")
        
        # Test FastAPI app
        from app.main import app
        print(f"✅ FastAPI application loaded: {app.title}")
        
        return True
    except Exception as e:
        print(f"❌ Application test failed: {e}")
        return False

def start_server():
    """Start the FastAPI server."""
    try:
        import uvicorn
        print("🚀 Starting FastAPI server...")
        print("📖 API Documentation will be available at: http://localhost:8000/docs")
        print("🏥 Health check available at: http://localhost:8000/health")
        print("Press Ctrl+C to stop the server")
        
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")

def main():
    """Main startup function."""
    print("🚀 UK Cheap Deal Backend - FastAPI E-commerce Application")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Create directories
    create_directories()
    
    # Check and install dependencies
    if not check_and_install_dependencies():
        print("❌ Failed to install dependencies")
        return 1
    
    # Test application
    if not test_application():
        print("❌ Application test failed")
        return 1
    
    print("\n✅ All checks passed! Starting server...")
    print("-" * 60)
    
    # Start server
    start_server()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
