# UK Cheap Deal Backend - E-commerce API

A comprehensive FastAPI-based e-commerce backend with Amazon Product Advertising API integration and AI-powered scraping capabilities.

## Features

- **User Management & Authentication**: JWT-based auth with role-based access control (User, Admin, Superadmin)
- **Product Management**: Full CRUD operations with Amazon PA-API integration
- **AI-Powered Scraping**: ScrapeGraphAI integration for multi-platform product scraping
- **Affiliate Link Management**: Amazon affiliate link generation and tracking
- **Rate Limiting & Security**: Built-in rate limiting, CORS, and security middleware
- **Comprehensive API**: RESTful endpoints with automatic OpenAPI documentation

## Quick Start

### Prerequisites

- Python 3.8+ (Python 3.11+ recommended)
- Amazon Product Advertising API credentials (configured for India region)
- Optional: Redis (for rate limiting and caching)
- Optional: ScrapeGraphAI API key

### Easy Installation & Startup

#### Option 1: Automated Startup (Recommended)

**Linux/macOS:**

```bash
chmod +x run.sh
./run.sh
```

**Windows:**

```cmd
run.bat
```

**Cross-platform Python:**

```bash
python3 start.py
```

#### Option 2: Manual Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd ukcheapdeal-backend-admin-dashboard-1
   ```

2. **Create virtual environment**

   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install core dependencies**

   ```bash
   pip install -r requirements-minimal.txt
   ```

4. **Configure environment (already configured with defaults)**

   ```bash
   # The .env file is already configured with working defaults
   # Amazon PA-API credentials are set for India region
   ```

5. **Run the application**
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

### Testing the Installation

Run the test script to verify everything is working:

```bash
python3 test_app.py
```

Or for a simple dependency check:

```bash
python3 simple_test.py
```

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up --build

# Or run individual container
docker build -t ukcheapdeal-backend .
docker run -p 8000:8000 ukcheapdeal-backend
```

## Project Structure

```
app/
├── main.py                 # FastAPI application entry point
├── config/
│   └── settings.py         # Application configuration
├── models/                 # SQLAlchemy database models
├── schemas/                # Pydantic request/response schemas
├── services/               # Business logic services
├── routes/                 # API route handlers
├── middleware/             # Custom middleware
├── utils/                  # Utility functions and exceptions
└── database/               # Database connection and configuration
```

## API Documentation

Once the application is running, visit:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Environment Variables

Key environment variables (see `.env.example` for complete list):

```env
# Database
DATABASE_URL=sqlite:///./app.db

# Security
SECRET_KEY=your-super-secret-key
JWT_EXPIRE_MINUTES=30

# Amazon PA-API
AMAZON_ACCESS_KEY=your-amazon-access-key
AMAZON_SECRET_KEY=your-amazon-secret-key
AMAZON_PARTNER_TAG=your-affiliate-tag

# ScrapeGraphAI
SCRAPEGRAPH_API_KEY=your-scrapegraph-key

# Redis
REDIS_URL=redis://localhost:6379/0
```

## User Roles

- **User**: View products, add favorites, submit reviews
- **Admin**: All user permissions + product management (CRUD)
- **Superadmin**: Full system access including user management

## API Endpoints

### Authentication

- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh token
- `POST /api/v1/auth/logout` - User logout

### Products

- `GET /api/v1/products` - List products (with filtering)
- `GET /api/v1/products/{id}` - Get product details
- `POST /api/v1/products` - Create product (Admin+)
- `PUT /api/v1/products/{id}` - Update product (Admin+)
- `DELETE /api/v1/products/{id}` - Delete product (Admin+)

### Users

- `GET /api/v1/users/profile` - Get user profile
- `PUT /api/v1/users/profile` - Update user profile
- `GET /api/v1/users/favorites` - Get user favorites
- `POST /api/v1/users/favorites/{product_id}` - Add to favorites

### Admin

- `GET /api/v1/admin/users` - List all users (Admin+)
- `PUT /api/v1/admin/users/{id}/role` - Update user role (Superadmin)
- `GET /api/v1/admin/analytics` - System analytics

### Scraping

- `POST /api/v1/scrape/products` - Initiate product scraping
- `GET /api/v1/scrape/jobs/{id}` - Check scraping job status
- `GET /api/v1/scrape/logs` - View scraping logs

## Development

### Running Tests

```bash
pytest
```

### Code Formatting

```bash
black app/
isort app/
flake8 app/
```

### Database Migrations

```bash
alembic init alembic
alembic revision --autogenerate -m "Initial migration"
alembic upgrade head
```

## Production Deployment

1. Set `DEBUG=false` in environment
2. Use a production database (PostgreSQL recommended)
3. Configure Redis for rate limiting
4. Set up proper logging and monitoring
5. Use a reverse proxy (nginx) for SSL termination
6. Configure environment-specific settings

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
