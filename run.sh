#!/bin/bash

# FastAPI E-commerce Backend Startup Script
echo "🚀 UK Cheap Deal Backend - FastAPI E-commerce Application"
echo "=========================================================="

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed or not in PATH"
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements-minimal.txt

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs uploads

# Run the application
echo "🚀 Starting FastAPI application..."
echo "📖 API Documentation: http://localhost:8000/docs"
echo "🏥 Health Check: http://localhost:8000/health"
echo "Press Ctrl+C to stop the server"
echo "--------------------------------------------------"

uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
