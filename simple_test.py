#!/usr/bin/env python3
"""
Very simple test to check basic functionality.
"""
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("Testing basic configuration...")

try:
    # Test environment loading
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded")
except ImportError:
    print("⚠️ python-dotenv not available, using system environment")
except Exception as e:
    print(f"❌ Environment error: {e}")

try:
    # Test basic imports
    import json
    import datetime
    print("✅ Basic Python modules work")
except Exception as e:
    print(f"❌ Basic import error: {e}")

try:
    # Test pydantic
    from pydantic import BaseModel
    print("✅ Pydantic available")
except ImportError:
    print("❌ Pydantic not available - install with: pip install pydantic")

try:
    # Test SQLAlchemy
    from sqlalchemy import create_engine
    print("✅ SQLAlchemy available")
except ImportError:
    print("❌ SQLAlchemy not available - install with: pip install sqlalchemy")

try:
    # Test FastAPI
    from fastapi import FastAPI
    print("✅ FastAPI available")
except ImportError:
    print("❌ FastAPI not available - install with: pip install fastapi")

print("\nDone!")
