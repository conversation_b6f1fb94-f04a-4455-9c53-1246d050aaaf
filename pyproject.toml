[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "ukcheapdeal-backend"
version = "1.0.0"
description = "FastAPI e-commerce backend with Amazon PA-API integration"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "UK Cheap Deal Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: POSIX :: Linux",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
]
requires-python = ">=3.8"

# Core production dependencies
dependencies = [
    # FastAPI and ASGI server
    "fastapi==0.104.1",
    "uvicorn[standard]==0.24.0",
    
    # Database
    "sqlalchemy==2.0.23",
    "alembic==1.12.1",
    
    # Authentication & Security
    "python-jose[cryptography]==3.3.0",
    "passlib[bcrypt]==1.7.4",
    "python-multipart==0.0.6",
    
    # Environment & Configuration
    "python-dotenv==1.0.0",
    "pydantic-settings==2.0.3",
    
    # HTTP Client for API integrations
    "httpx==0.25.2",
    "aiohttp==3.9.1",
    
    # Amazon PA-API
    "paapi5-python-sdk==1.2.0",
    
    # Rate limiting & Caching
    "slowapi==0.1.9",
    "redis==5.0.1",
    
    # Utilities
    "python-slugify==8.0.1",
    "pillow==10.1.0",
    "validators==0.22.0",
]

[project.optional-dependencies]
# Development dependencies
dev = [
    # Testing
    "pytest==7.4.3",
    "pytest-asyncio==0.21.1",
    "pytest-cov==4.1.0",
    "pytest-xdist==3.3.1",  # Parallel test execution
    "pytest-mock==3.12.0",  # Mocking utilities
    
    # Code quality
    "black==23.11.0",
    "isort==5.12.0",
    "flake8==6.1.0",
    "mypy==1.7.1",
    "pre-commit==3.5.0",
    
    # Development tools
    "watchfiles==0.21.0",  # File watching for hot reload
    "ipython==8.17.2",     # Enhanced REPL
    "rich==13.7.0",        # Beautiful terminal output
]

# Scraping and AI dependencies (optional for advanced features)
scraping = [
    "scrapegraphai==1.0.0",
    "beautifulsoup4==4.12.2",
    "selenium==4.15.2",
    "fake-useragent==1.4.0",
]

# Documentation dependencies
docs = [
    "mkdocs==1.5.3",
    "mkdocs-material==9.4.8",
    "mkdocs-swagger-ui-tag==0.6.6",
]

# Production monitoring and logging
monitoring = [
    "prometheus-client==0.19.0",
    "structlog==23.2.0",
    "sentry-sdk[fastapi]==1.38.0",
]

# All optional dependencies combined
all = [
    "ukcheapdeal-backend[dev,scraping,docs,monitoring]"
]

[project.urls]
Homepage = "https://github.com/ukcheapdeal/backend"
Documentation = "https://docs.ukcheapdeal.com"
Repository = "https://github.com/ukcheapdeal/backend.git"
Issues = "https://github.com/ukcheapdeal/backend/issues"

[project.scripts]
ukcheapdeal = "app.main:main"

# UV-specific configuration
[tool.uv]
dev-dependencies = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
]

# Python version management
python-preference = "only-managed"
python-downloads = "automatic"

# Package resolution
resolution = "highest"
prerelease = "disallow"

# Index configuration for faster package resolution
index-strategy = "first-index"
keyring-provider = "disabled"

# Build configuration
compile-bytecode = true
no-build-isolation = false

# Cache configuration
cache-dir = ".uv-cache"

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
    "--cov-fail-under=80",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "auth: marks tests related to authentication",
    "api: marks tests related to API endpoints",
    "database: marks tests related to database operations",
]
asyncio_mode = "auto"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/.venv/*",
    "*/migrations/*",
    "*/alembic/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
  | alembic
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88
skip_gitignore = true
skip_glob = ["*/migrations/*", "*/alembic/*"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    "*.egg-info",
    ".venv",
    "venv",
    "migrations",
    "alembic",
]

[tool.mypy]
python_version = "3.8"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true
exclude = [
    "migrations/",
    "alembic/",
    "tests/",
]

[[tool.mypy.overrides]]
module = [
    "paapi5_python_sdk.*",
    "scrapegraphai.*",
    "slowapi.*",
    "fake_useragent.*",
]
ignore_missing_imports = true
