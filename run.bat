@echo off
REM FastAPI E-commerce Backend Startup Script for Windows

echo 🚀 UK Cheap Deal Backend - FastAPI E-commerce Application
echo ==========================================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    pause
    exit /b 1
)

echo ✅ Python found
python --version

REM Create virtual environment if it doesn't exist
if not exist "venv" (
    echo 📦 Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
echo 🔧 Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip
echo ⬆️ Upgrading pip...
python -m pip install --upgrade pip

REM Install dependencies
echo 📦 Installing dependencies...
pip install -r requirements-minimal.txt

REM Create necessary directories
echo 📁 Creating directories...
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads

REM Run the application
echo 🚀 Starting FastAPI application...
echo 📖 API Documentation: http://localhost:8000/docs
echo 🏥 Health Check: http://localhost:8000/health
echo Press Ctrl+C to stop the server
echo --------------------------------------------------

uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

pause
