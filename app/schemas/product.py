"""
Product-related Pydantic schemas.
"""
from typing import List, Optional
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, validator, Field


class ProductImageCreate(BaseModel):
    """Schema for creating product image."""
    image_url: str = Field(..., description="URL of the product image")
    alt_text: Optional[str] = Field(None, description="Alternative text for the image")
    title: Optional[str] = Field(None, description="Title of the image")
    is_primary: bool = Field(False, description="Whether this is the primary image")
    sort_order: int = Field(0, description="Sort order for image display")


class ProductImageUpdate(BaseModel):
    """Schema for updating product image."""
    alt_text: Optional[str] = None
    title: Optional[str] = None
    is_primary: Optional[bool] = None
    sort_order: Optional[int] = None


class ProductImageResponse(BaseModel):
    """Schema for product image response."""
    id: int
    image_url: str
    alt_text: Optional[str]
    title: Optional[str]
    is_primary: bool
    sort_order: int
    width: Optional[int]
    height: Optional[int]
    format: Optional[str]

    class Config:
        orm_mode = True


class ProductPriceCreate(BaseModel):
    """Schema for creating product price."""
    current_price: Decimal = Field(..., description="Current price of the product")
    original_price: Optional[Decimal] = Field(None, description="Original price before discount")
    currency: str = Field("GBP", description="Currency code")
    source: Optional[str] = Field("manual", description="Source of the price data")


class ProductPriceUpdate(BaseModel):
    """Schema for updating product price."""
    current_price: Optional[Decimal] = None
    original_price: Optional[Decimal] = None
    currency: Optional[str] = None


class ProductPriceResponse(BaseModel):
    """Schema for product price response."""
    id: int
    current_price: Decimal
    original_price: Optional[Decimal]
    currency: str
    discount_percentage: Optional[float]
    savings: Decimal
    savings_percentage: float
    source: Optional[str]
    updated_at: datetime

    class Config:
        orm_mode = True


class CategoryResponse(BaseModel):
    """Schema for category response."""
    id: int
    name: str
    slug: str
    full_path: str
    level: int

    class Config:
        orm_mode = True


class StoreResponse(BaseModel):
    """Schema for store response."""
    id: int
    name: str
    display_name: str
    base_url: str

    class Config:
        orm_mode = True


class ProductCreate(BaseModel):
    """Schema for creating a product."""
    title: str = Field(..., min_length=1, max_length=500, description="Product title")
    description: Optional[str] = Field(None, description="Detailed product description")
    short_description: Optional[str] = Field(None, max_length=500, description="Short product description")

    # External references
    external_id: Optional[str] = Field(None, description="External product ID")
    asin: Optional[str] = Field(None, max_length=20, description="Amazon ASIN")
    sku: Optional[str] = Field(None, max_length=100, description="Product SKU")

    # Store and category
    store_id: int = Field(..., description="Store ID")
    category_id: Optional[int] = Field(None, description="Category ID")

    # Product details
    brand: Optional[str] = Field(None, max_length=100, description="Product brand")
    model: Optional[str] = Field(None, max_length=100, description="Product model")
    manufacturer: Optional[str] = Field(None, max_length=100, description="Product manufacturer")

    # Availability
    is_available: bool = Field(True, description="Product availability")
    stock_quantity: Optional[int] = Field(None, ge=0, description="Stock quantity")
    availability_status: Optional[str] = Field(None, description="Availability status")

    # Shipping
    shipping_weight: Optional[float] = Field(None, ge=0, description="Shipping weight in kg")
    shipping_dimensions: Optional[str] = Field(None, description="Shipping dimensions (L x W x H)")
    free_shipping: bool = Field(False, description="Free shipping available")

    # URLs
    product_url: Optional[str] = Field(None, description="Product URL")

    # Status
    is_featured: bool = Field(False, description="Featured product")

    # Initial price (optional)
    initial_price: Optional[ProductPriceCreate] = None

    # Initial images (optional)
    initial_images: Optional[List[ProductImageCreate]] = None


class ProductUpdate(BaseModel):
    """Schema for updating a product."""
    title: Optional[str] = Field(None, min_length=1, max_length=500)
    description: Optional[str] = None
    short_description: Optional[str] = Field(None, max_length=500)

    # Product details
    brand: Optional[str] = Field(None, max_length=100)
    model: Optional[str] = Field(None, max_length=100)
    manufacturer: Optional[str] = Field(None, max_length=100)

    # Category
    category_id: Optional[int] = None

    # Availability
    is_available: Optional[bool] = None
    stock_quantity: Optional[int] = Field(None, ge=0)
    availability_status: Optional[str] = None

    # Shipping
    shipping_weight: Optional[float] = Field(None, ge=0)
    shipping_dimensions: Optional[str] = None
    free_shipping: Optional[bool] = None

    # URLs
    product_url: Optional[str] = None

    # Status
    is_featured: Optional[bool] = None


class ProductResponse(BaseModel):
    """Schema for detailed product response."""
    id: int
    title: str
    description: Optional[str]
    short_description: Optional[str]
    external_id: Optional[str]
    asin: Optional[str]
    sku: Optional[str]
    brand: Optional[str]
    model: Optional[str]
    manufacturer: Optional[str]
    is_available: bool
    stock_quantity: Optional[int]
    availability_status: Optional[str]
    shipping_weight: Optional[float]
    shipping_dimensions: Optional[str]
    free_shipping: bool
    slug: Optional[str]
    product_url: Optional[str]
    is_active: bool
    is_featured: bool
    average_rating: float
    review_count: int
    created_at: datetime
    updated_at: datetime

    # Relationships
    store: Optional[StoreResponse]
    category: Optional[CategoryResponse]
    current_price: Optional[ProductPriceResponse]
    prices: List[ProductPriceResponse] = []
    images: List[ProductImageResponse] = []

    class Config:
        orm_mode = True


class ProductListResponse(BaseModel):
    """Schema for product list response (simplified)."""
    id: int
    title: str
    short_description: Optional[str]
    brand: Optional[str]
    is_available: bool
    is_featured: bool
    average_rating: float
    review_count: int
    slug: Optional[str]
    created_at: datetime

    # Simplified relationships
    store_name: Optional[str]
    category_name: Optional[str]
    current_price: Optional[Decimal]
    currency: Optional[str]
    primary_image_url: Optional[str]

    class Config:
        orm_mode = True


class ProductSearchFilters(BaseModel):
    """Schema for product search filters."""
    query: Optional[str] = Field(None, description="Search query")
    category_id: Optional[int] = Field(None, description="Filter by category")
    store_id: Optional[int] = Field(None, description="Filter by store")
    brand: Optional[str] = Field(None, description="Filter by brand")
    min_price: Optional[Decimal] = Field(None, ge=0, description="Minimum price")
    max_price: Optional[Decimal] = Field(None, ge=0, description="Maximum price")
    min_rating: Optional[float] = Field(None, ge=0, le=5, description="Minimum rating")
    is_available: Optional[bool] = Field(None, description="Filter by availability")
    is_featured: Optional[bool] = Field(None, description="Filter by featured status")
    free_shipping: Optional[bool] = Field(None, description="Filter by free shipping")

    @validator('max_price')
    def validate_price_range(cls, v, values):
        """Validate that max_price is greater than min_price."""
        if v is not None and 'min_price' in values and values['min_price'] is not None:
            if v < values['min_price']:
                raise ValueError('max_price must be greater than min_price')
        return v


class ProductListRequest(BaseModel):
    """Schema for product list request with pagination and sorting."""
    skip: int = Field(0, ge=0, description="Number of items to skip")
    limit: int = Field(50, ge=1, le=100, description="Number of items to return")
    sort_by: str = Field("created_at", description="Field to sort by")
    sort_order: str = Field("desc", regex="^(asc|desc)$", description="Sort order")
    filters: Optional[ProductSearchFilters] = None
