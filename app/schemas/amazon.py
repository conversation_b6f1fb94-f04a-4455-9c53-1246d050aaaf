"""
Amazon PA-API related Pydantic schemas.
"""
from typing import List, Optional, Dict, Any
from decimal import Decimal
from pydantic import BaseModel, Field, validator


class AmazonSearchRequest(BaseModel):
    """Schema for Amazon product search request."""
    keywords: str = Field(..., min_length=1, max_length=200, description="Search keywords")
    search_index: str = Field("All", description="Amazon search index/category")
    max_results: int = Field(10, ge=1, le=10, description="Maximum number of results")
    sort_by: Optional[str] = Field(None, description="Sort criteria")
    min_price: Optional[Decimal] = Field(None, ge=0, description="Minimum price filter")
    max_price: Optional[Decimal] = Field(None, ge=0, description="Maximum price filter")
    
    @validator('max_price')
    def validate_price_range(cls, v, values):
        """Validate that max_price is greater than min_price."""
        if v is not None and 'min_price' in values and values['min_price'] is not None:
            if v < values['min_price']:
                raise ValueError('max_price must be greater than min_price')
        return v


class AmazonPriceInfo(BaseModel):
    """Schema for Amazon price information."""
    amount: Decimal
    currency: str
    display: str
    savings: Optional[Dict[str, Any]] = None


class AmazonImageInfo(BaseModel):
    """Schema for Amazon image information."""
    primary: str
    variants: List[str] = []


class AmazonShippingInfo(BaseModel):
    """Schema for Amazon shipping information."""
    free_shipping: bool = False
    prime_eligible: bool = False
    delivery_info: Optional[str] = None


class AmazonDimensions(BaseModel):
    """Schema for Amazon product dimensions."""
    height: Optional[str] = None
    length: Optional[str] = None
    width: Optional[str] = None
    weight: Optional[str] = None


class AmazonCategoryInfo(BaseModel):
    """Schema for Amazon category information."""
    primary: str
    browse_nodes: List[str] = []


class AmazonProductResponse(BaseModel):
    """Schema for Amazon product response."""
    asin: str
    title: str
    brand: Optional[str] = None
    manufacturer: Optional[str] = None
    model: Optional[str] = None
    description: Optional[str] = None
    features: List[str] = []
    price: Optional[AmazonPriceInfo] = None
    images: Optional[AmazonImageInfo] = None
    url: str
    rating: Optional[float] = Field(None, ge=0, le=5)
    review_count: Optional[int] = Field(None, ge=0)
    availability: Optional[str] = None
    shipping: Optional[AmazonShippingInfo] = None
    dimensions: Optional[AmazonDimensions] = None
    category: Optional[AmazonCategoryInfo] = None


class AmazonSearchResponse(BaseModel):
    """Schema for Amazon search response."""
    products: List[AmazonProductResponse]
    total_results: int
    search_terms: str
    search_index: str


class AmazonBrowseNodeRequest(BaseModel):
    """Schema for Amazon browse node request."""
    browse_node_id: str = Field(..., description="Amazon browse node ID")


class AmazonBrowseNodeChild(BaseModel):
    """Schema for Amazon browse node child."""
    id: str
    name: str
    display_name: str


class AmazonBrowseNodeAncestor(BaseModel):
    """Schema for Amazon browse node ancestor."""
    id: str
    name: str
    display_name: str


class AmazonBrowseNodeResponse(BaseModel):
    """Schema for Amazon browse node response."""
    id: str
    name: str
    context_free_name: str
    display_name: str
    is_root: bool
    children: List[AmazonBrowseNodeChild] = []
    ancestor: Optional[AmazonBrowseNodeAncestor] = None


class AmazonVariationAttributes(BaseModel):
    """Schema for Amazon product variation attributes."""
    color: Optional[str] = None
    size: Optional[str] = None
    style: Optional[str] = None
    # Add more variation attributes as needed


class AmazonProductVariation(BaseModel):
    """Schema for Amazon product variation."""
    asin: str
    title: str
    variation_attributes: Dict[str, str]
    price: Optional[AmazonPriceInfo] = None
    images: Optional[AmazonImageInfo] = None
    url: str
    availability: Optional[str] = None


class AmazonVariationsResponse(BaseModel):
    """Schema for Amazon product variations response."""
    parent_asin: str
    variations: List[AmazonProductVariation]


class AmazonBatchRequest(BaseModel):
    """Schema for Amazon batch product request."""
    asins: List[str] = Field(..., min_items=1, max_items=10, description="List of ASINs")
    
    @validator('asins')
    def validate_asins(cls, v):
        """Validate ASIN format."""
        for asin in v:
            if not asin or len(asin) != 10:
                raise ValueError(f"Invalid ASIN format: {asin}")
        return v


class AmazonBatchResponse(BaseModel):
    """Schema for Amazon batch product response."""
    products: List[AmazonProductResponse]
    requested_asins: List[str]
    found_asins: List[str]
    not_found_asins: List[str]


class AmazonAffiliateLinkRequest(BaseModel):
    """Schema for Amazon affiliate link generation request."""
    asin: str = Field(..., description="Amazon ASIN")
    additional_params: Optional[Dict[str, str]] = Field(None, description="Additional URL parameters")


class AmazonAffiliateLinkResponse(BaseModel):
    """Schema for Amazon affiliate link response."""
    asin: str
    affiliate_url: str
    partner_tag: str
    created_at: str


class AmazonImportRequest(BaseModel):
    """Schema for importing Amazon product to local database."""
    asin: str = Field(..., description="Amazon ASIN to import")
    store_id: int = Field(..., description="Local store ID")
    category_id: Optional[int] = Field(None, description="Local category ID")
    override_title: Optional[str] = Field(None, description="Override product title")
    override_description: Optional[str] = Field(None, description="Override product description")
    is_featured: bool = Field(False, description="Mark as featured product")


class AmazonImportResponse(BaseModel):
    """Schema for Amazon product import response."""
    success: bool
    product_id: Optional[int] = None
    asin: str
    message: str
    warnings: List[str] = []


class AmazonSyncRequest(BaseModel):
    """Schema for syncing Amazon product data."""
    product_ids: List[int] = Field(..., min_items=1, max_items=50, description="Local product IDs to sync")
    sync_prices: bool = Field(True, description="Sync price information")
    sync_availability: bool = Field(True, description="Sync availability status")
    sync_images: bool = Field(False, description="Sync product images")
    sync_reviews: bool = Field(False, description="Sync review counts and ratings")


class AmazonSyncResponse(BaseModel):
    """Schema for Amazon product sync response."""
    total_products: int
    successful_syncs: int
    failed_syncs: int
    sync_results: List[Dict[str, Any]]
    warnings: List[str] = []
