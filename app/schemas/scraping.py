"""
Scraping-related Pydantic schemas.
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from app.models.scraping_job import JobStatus


class ScrapingJobCreate(BaseModel):
    """Schema for creating a scraping job."""
    job_type: str = Field(..., description="Type of scraping job")
    target_url: Optional[str] = Field(None, description="Target URL to scrape")
    target_store: Optional[str] = Field(None, description="Target store name")
    search_query: Optional[str] = Field(None, description="Search query")
    config: Optional[Dict[str, Any]] = Field(None, description="Additional configuration")
    
    @validator('job_type')
    def validate_job_type(cls, v):
        """Validate job type."""
        allowed_types = ["product_search", "product_details", "category_scraping", "price_monitoring"]
        if v not in allowed_types:
            raise ValueError(f"Job type must be one of: {', '.join(allowed_types)}")
        return v
    
    @validator('target_store')
    def validate_target_store(cls, v):
        """Validate target store."""
        if v is not None:
            allowed_stores = ["amazon", "ebay", "walmart", "flipkart", "myntra"]
            if v.lower() not in allowed_stores:
                raise ValueError(f"Target store must be one of: {', '.join(allowed_stores)}")
        return v.lower() if v else v


class ProductSearchRequest(BaseModel):
    """Schema for product search scraping request."""
    search_query: str = Field(..., min_length=1, max_length=200, description="Search query")
    target_stores: List[str] = Field(..., min_items=1, description="List of stores to search")
    max_results_per_store: int = Field(20, ge=1, le=50, description="Maximum results per store")
    filters: Optional[Dict[str, Any]] = Field(None, description="Additional filters")
    
    @validator('target_stores')
    def validate_stores(cls, v):
        """Validate target stores."""
        allowed_stores = ["amazon", "ebay", "walmart", "flipkart", "myntra"]
        for store in v:
            if store.lower() not in allowed_stores:
                raise ValueError(f"Store '{store}' not supported. Allowed: {', '.join(allowed_stores)}")
        return [store.lower() for store in v]


class ScrapedProductData(BaseModel):
    """Schema for scraped product data."""
    platform: str
    title: str
    price: Optional[str] = None
    currency: Optional[str] = None
    rating: Optional[float] = Field(None, ge=0, le=5)
    review_count: Optional[int] = Field(None, ge=0)
    availability: Optional[str] = None
    brand: Optional[str] = None
    image_url: Optional[str] = None
    product_url: Optional[str] = None
    scraped_at: str
    
    # Platform-specific fields
    asin: Optional[str] = None  # Amazon
    condition: Optional[str] = None  # eBay
    seller: Optional[str] = None  # eBay
    prime_eligible: Optional[bool] = None  # Amazon
    free_shipping: Optional[bool] = None


class ScrapingJobResponse(BaseModel):
    """Schema for scraping job response."""
    job_id: int
    status: str
    job_type: str
    target_store: Optional[str]
    search_query: Optional[str]
    progress_percentage: float
    success_rate: float
    total_items: int
    processed_items: int
    successful_items: int
    failed_items: int
    started_at: Optional[str]
    completed_at: Optional[str]
    duration: Optional[str]
    error_message: Optional[str]


class ScrapingJobListResponse(BaseModel):
    """Schema for scraping job list response."""
    job_id: int
    status: str
    job_type: str
    target_store: Optional[str]
    search_query: Optional[str]
    progress_percentage: float
    created_at: datetime
    completed_at: Optional[datetime]
    
    class Config:
        orm_mode = True


class ScrapingLogResponse(BaseModel):
    """Schema for scraping log response."""
    id: int
    job_id: int
    level: str
    message: str
    details: Optional[Dict[str, Any]]
    url: Optional[str]
    step: Optional[str]
    created_at: datetime
    
    class Config:
        orm_mode = True


class BatchScrapingRequest(BaseModel):
    """Schema for batch scraping request."""
    jobs: List[ScrapingJobCreate] = Field(..., min_items=1, max_items=10)
    priority: str = Field("normal", description="Job priority")
    
    @validator('priority')
    def validate_priority(cls, v):
        """Validate priority."""
        allowed_priorities = ["low", "normal", "high", "urgent"]
        if v not in allowed_priorities:
            raise ValueError(f"Priority must be one of: {', '.join(allowed_priorities)}")
        return v


class ScrapingStatsResponse(BaseModel):
    """Schema for scraping statistics response."""
    total_jobs: int
    completed_jobs: int
    failed_jobs: int
    running_jobs: int
    pending_jobs: int
    success_rate: float
    average_duration_minutes: float
    total_products_scraped: int
    jobs_by_store: Dict[str, int]
    jobs_by_type: Dict[str, int]


class PriceMonitoringRequest(BaseModel):
    """Schema for price monitoring request."""
    product_urls: List[str] = Field(..., min_items=1, max_items=100)
    monitoring_frequency: str = Field("daily", description="Monitoring frequency")
    price_change_threshold: float = Field(5.0, ge=0, le=100, description="Price change threshold percentage")
    
    @validator('monitoring_frequency')
    def validate_frequency(cls, v):
        """Validate monitoring frequency."""
        allowed_frequencies = ["hourly", "daily", "weekly"]
        if v not in allowed_frequencies:
            raise ValueError(f"Frequency must be one of: {', '.join(allowed_frequencies)}")
        return v


class CategoryScrapingRequest(BaseModel):
    """Schema for category scraping request."""
    store: str = Field(..., description="Target store")
    category_url: str = Field(..., description="Category URL to scrape")
    max_pages: int = Field(5, ge=1, le=20, description="Maximum pages to scrape")
    include_subcategories: bool = Field(False, description="Include subcategories")
    
    @validator('store')
    def validate_store(cls, v):
        """Validate store."""
        allowed_stores = ["amazon", "ebay", "walmart", "flipkart", "myntra"]
        if v.lower() not in allowed_stores:
            raise ValueError(f"Store must be one of: {', '.join(allowed_stores)}")
        return v.lower()


class ScrapingConfigUpdate(BaseModel):
    """Schema for updating scraping configuration."""
    delay_min: Optional[float] = Field(None, ge=0.5, le=10)
    delay_max: Optional[float] = Field(None, ge=1, le=30)
    concurrent_requests: Optional[int] = Field(None, ge=1, le=20)
    timeout: Optional[int] = Field(None, ge=10, le=300)
    retry_attempts: Optional[int] = Field(None, ge=1, le=5)
    
    @validator('delay_max')
    def validate_delay_range(cls, v, values):
        """Validate that max delay is greater than min delay."""
        if v is not None and 'delay_min' in values and values['delay_min'] is not None:
            if v < values['delay_min']:
                raise ValueError('delay_max must be greater than delay_min')
        return v


class ScrapingQueueStatus(BaseModel):
    """Schema for scraping queue status."""
    queue_size: int
    active_jobs: int
    pending_jobs: int
    estimated_completion_time: Optional[str]
    worker_status: List[Dict[str, Any]]


class ScrapingJobResult(BaseModel):
    """Schema for scraping job results."""
    job_id: int
    status: str
    results_count: int
    results: List[ScrapedProductData]
    execution_time_seconds: float
    errors: List[str] = []
    warnings: List[str] = []
