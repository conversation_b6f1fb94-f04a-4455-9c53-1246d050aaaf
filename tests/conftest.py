"""
Test configuration and fixtures.
"""
import pytest
import tempfile
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

from app.main import app
from app.database.connection import get_db, Base
from app.models.user import User, UserRole
from app.utils.security import get_password_hash


@pytest.fixture(scope="session")
def test_db():
    """Create a test database."""
    # Create temporary database file
    db_fd, db_path = tempfile.mkstemp()
    database_url = f"sqlite:///{db_path}"
    
    # Create engine and tables
    engine = create_engine(database_url, connect_args={"check_same_thread": False})
    Base.metadata.create_all(bind=engine)
    
    yield engine
    
    # Cleanup
    os.close(db_fd)
    os.unlink(db_path)


@pytest.fixture
def db_session(test_db):
    """Create a database session for testing."""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_db)
    session = TestingSessionLocal()
    
    yield session
    
    session.close()


@pytest.fixture
def client(db_session):
    """Create a test client."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture
def test_user(db_session):
    """Create a test user."""
    user = User(
        email="<EMAIL>",
        password_hash=get_password_hash("testpassword123"),
        first_name="Test",
        last_name="User",
        role=UserRole.USER,
        is_active=True,
        is_verified=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_admin(db_session):
    """Create a test admin user."""
    admin = User(
        email="<EMAIL>",
        password_hash=get_password_hash("adminpassword123"),
        first_name="Admin",
        last_name="User",
        role=UserRole.ADMIN,
        is_active=True,
        is_verified=True
    )
    db_session.add(admin)
    db_session.commit()
    db_session.refresh(admin)
    return admin


@pytest.fixture
def test_superadmin(db_session):
    """Create a test superadmin user."""
    superadmin = User(
        email="<EMAIL>",
        password_hash=get_password_hash("superadminpassword123"),
        first_name="Super",
        last_name="Admin",
        role=UserRole.SUPERADMIN,
        is_active=True,
        is_verified=True
    )
    db_session.add(superadmin)
    db_session.commit()
    db_session.refresh(superadmin)
    return superadmin


@pytest.fixture
def auth_headers(client, test_user):
    """Get authentication headers for test user."""
    response = client.post("/api/v1/auth/login", json={
        "email": test_user.email,
        "password": "testpassword123"
    })
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def admin_headers(client, test_admin):
    """Get authentication headers for admin user."""
    response = client.post("/api/v1/auth/login", json={
        "email": test_admin.email,
        "password": "adminpassword123"
    })
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def superadmin_headers(client, test_superadmin):
    """Get authentication headers for superadmin user."""
    response = client.post("/api/v1/auth/login", json={
        "email": test_superadmin.email,
        "password": "superadminpassword123"
    })
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def sample_product_data():
    """Sample product data for testing."""
    return {
        "title": "Test Product",
        "description": "This is a test product description",
        "short_description": "Test product",
        "store_id": 1,
        "brand": "Test Brand",
        "is_available": True,
        "is_featured": False
    }
