# Migration Guide: From pip to UV Package Manager

This guide helps you migrate from the old pip-based workflow to the new UV-based development environment.

## 🚀 What's New

### UV Package Manager
- **Lightning-fast** dependency installation (10-100x faster than pip)
- **Better dependency resolution** with conflict detection
- **Unified tool** for package management and virtual environments
- **Production-ready** with lock files and reproducible builds

### Docker-First Development
- **Separate environments** for development and production
- **Hot reload** support in development
- **Integrated testing** with dedicated test containers
- **Production-ready** with monitoring and backup support

### Linux-Optimized
- **Removed Windows support** for streamlined deployment
- **Optimized for Linux** servers and containers
- **Better performance** with Linux-specific optimizations

## 📋 Migration Steps

### 1. Remove Old Files (Already Done)
- ❌ `run.bat` (Windows batch script)
- ❌ `requirements-minimal.txt` (replaced by pyproject.toml)
- ❌ `simple_test.py` and `test_app.py` (replaced by comprehensive test suite)

### 2. New File Structure
```
✅ pyproject.toml              # UV package configuration
✅ docker-compose.dev.yml      # Development environment
✅ docker-compose.prod.yml     # Production environment
✅ run_uv.sh                   # UV-based startup script
✅ start_uv.py                 # UV-based Python startup
✅ scripts/docker-dev.sh       # Docker development management
✅ tests/                      # Comprehensive test suite
   ├── test_auth.py
   ├── test_products.py
   ├── test_users.py
   ├── test_admin.py
   ├── test_health.py
   └── test_amazon_api.py
```

### 3. New Development Workflow

#### Option 1: Docker (Recommended)
```bash
# Start development environment
./scripts/docker-dev.sh start

# View logs
./scripts/docker-dev.sh logs app

# Run tests
./scripts/docker-dev.sh test

# Execute commands
./scripts/docker-dev.sh exec bash
```

#### Option 2: UV Native Development
```bash
# Install UV (if not installed)
curl -LsSf https://astral.sh/uv/install.sh | sh

# Start application
./run_uv.sh
# OR
python3 start_uv.py
```

#### Option 3: Manual UV Setup
```bash
# Install UV
curl -LsSf https://astral.sh/uv/install.sh | sh

# Create virtual environment
uv venv .venv
source .venv/bin/activate

# Install dependencies
uv pip install -e ".[dev]"

# Run application
uv run uvicorn app.main:app --reload
```

## 🔄 Command Equivalents

### Old vs New Commands

| Old Command | New Command (Docker) | New Command (UV) |
|-------------|---------------------|------------------|
| `./run.sh` | `./scripts/docker-dev.sh start` | `./run_uv.sh` |
| `pip install -r requirements.txt` | `./scripts/docker-dev.sh install` | `uv pip install -e ".[dev]"` |
| `pytest` | `./scripts/docker-dev.sh test` | `uv run pytest` |
| `black app/` | `./scripts/docker-dev.sh format` | `uv run black app/` |
| `docker-compose up` | `docker-compose -f docker-compose.dev.yml up` | N/A |

### Package Management

| Task | Old Command | New Command |
|------|-------------|-------------|
| Add dependency | Edit requirements.txt + pip install | `uv add package-name` |
| Add dev dependency | Edit requirements.txt + pip install | `uv add --dev package-name` |
| Remove dependency | Edit requirements.txt + pip uninstall | `uv remove package-name` |
| Update dependencies | `pip install -U -r requirements.txt` | `uv pip sync` |
| Lock dependencies | Manual requirements.txt | Automatic with uv.lock |

## 🧪 Testing Changes

### Old Testing
```bash
python3 test_app.py
python3 simple_test.py
pytest  # if installed
```

### New Testing
```bash
# Docker testing
./scripts/docker-dev.sh test

# UV testing
uv run pytest
uv run pytest --cov=app
uv run pytest -m "unit"
uv run pytest -m "integration"
```

### Test Organization
- **Comprehensive coverage**: Authentication, products, users, admin, health, Amazon API
- **Organized by feature**: Each test file covers a specific domain
- **Fixtures and mocks**: Proper test isolation and mocking
- **Performance tests**: Marked with `@pytest.mark.slow`
- **Integration tests**: Marked with `@pytest.mark.integration`

## 🐳 Docker Changes

### Development Environment
- **Hot reload**: Code changes automatically restart the server
- **Volume mounts**: Source code is mounted for live editing
- **Integrated services**: Redis, PostgreSQL (optional), monitoring
- **Development tools**: Separate container for development utilities

### Production Environment
- **Multi-stage builds**: Optimized for production
- **Security**: Non-root user, minimal attack surface
- **Monitoring**: Prometheus and Grafana integration
- **Backup**: Automated database backup service
- **Load balancing**: Nginx reverse proxy

## 🔧 Configuration Changes

### Environment Variables
- **Structured configuration**: Better organization in .env files
- **Environment-specific**: Separate configs for dev/prod/test
- **Security**: Proper secret management in production

### Package Configuration
- **pyproject.toml**: Modern Python packaging standard
- **Dependency groups**: Separate dev, docs, monitoring dependencies
- **Tool configuration**: Black, isort, pytest, mypy configs in one file

## 🚨 Breaking Changes

### Removed Features
- **Windows support**: Use WSL2 or Linux environment
- **pip-based installation**: Use UV for better performance
- **Simple test scripts**: Use comprehensive pytest suite

### Changed Behavior
- **Virtual environment location**: `.venv` instead of `venv`
- **Dependency management**: pyproject.toml instead of requirements.txt
- **Docker compose**: Separate files for dev/prod instead of single file

## 🔄 Rollback Plan

If you need to rollback to the old system:

1. **Restore old files**:
   ```bash
   git checkout HEAD~1 -- requirements.txt requirements-minimal.txt
   git checkout HEAD~1 -- run.bat simple_test.py test_app.py
   ```

2. **Use legacy scripts**:
   ```bash
   ./run.sh  # Still available but deprecated
   python3 start.py  # Still available but deprecated
   ```

3. **Old Docker**:
   ```bash
   docker-compose up  # Uses deprecated docker-compose.yml
   ```

## 📚 Additional Resources

- [UV Documentation](https://github.com/astral-sh/uv)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [FastAPI Testing](https://fastapi.tiangolo.com/tutorial/testing/)
- [pytest Documentation](https://docs.pytest.org/)

## 🆘 Troubleshooting

### UV Installation Issues
```bash
# Reinstall UV
curl -LsSf https://astral.sh/uv/install.sh | sh
source ~/.bashrc
```

### Docker Issues
```bash
# Reset Docker environment
./scripts/docker-dev.sh clean
docker system prune -f
```

### Permission Issues
```bash
# Fix Docker permissions
sudo usermod -aG docker $USER
newgrp docker
```

### Port Conflicts
```bash
# Find and kill process using port
sudo lsof -i :8000
sudo kill -9 <PID>
```

## ✅ Verification

After migration, verify everything works:

1. **Start development environment**:
   ```bash
   ./scripts/docker-dev.sh start
   ```

2. **Check health**:
   ```bash
   curl http://localhost:8000/health
   ```

3. **Run tests**:
   ```bash
   ./scripts/docker-dev.sh test
   ```

4. **Check API docs**:
   Open http://localhost:8000/docs

If all steps pass, migration is complete! 🎉
