#!/usr/bin/env python3
"""
Simple test script to check if the FastAPI application can start.
"""
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """Test basic Python imports."""
    try:
        print("🔍 Testing basic Python imports...")
        import json
        import datetime
        import logging
        print("✅ Basic Python modules imported successfully!")
        return True
    except Exception as e:
        print(f"❌ Basic import error: {e}")
        return False

def test_config():
    """Test configuration loading."""
    try:
        print("🔍 Testing configuration...")
        from app.config.settings import get_settings
        settings = get_settings()
        print(f"✅ Settings loaded: {settings.app_name} v{settings.app_version}")
        print(f"✅ Database URL: {settings.database_url}")
        print(f"✅ Debug mode: {settings.debug}")
        return True
    except Exception as e:
        print(f"❌ Config error: {e}")
        return False

def test_database():
    """Test database connection."""
    try:
        print("🔍 Testing database connection...")
        from app.database.connection import engine, Base
        print("✅ Database engine created successfully!")
        return True
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_models():
    """Test models import."""
    try:
        print("🔍 Testing models import...")
        import app.models
        print("✅ Models imported successfully!")
        return True
    except Exception as e:
        print(f"❌ Models error: {e}")
        return False

def test_fastapi():
    """Test FastAPI application."""
    try:
        print("🔍 Testing FastAPI application...")
        from app.main import app
        print("✅ FastAPI application imported successfully!")
        print(f"✅ App title: {app.title}")
        return True
    except Exception as e:
        print(f"❌ FastAPI error: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting FastAPI application tests...\n")

    tests = [
        test_basic_imports,
        test_config,
        test_database,
        test_models,
        test_fastapi
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1
        print()

    print(f"📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("\n🎉 All tests passed! The application should be ready to run.")
        print("\nTo start the application, run:")
        print("uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        return 0
    else:
        print(f"\n❌ {total - passed} tests failed. Please fix the issues above.")
        print("\nMake sure all dependencies are installed:")
        print("pip install -r requirements.txt")
        return 1

if __name__ == "__main__":
    sys.exit(main())
